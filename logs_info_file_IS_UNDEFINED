2025-06-05 11:51:14.840|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-05 11:51:14.854|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 16920 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-05 11:51:14.854|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-05 11:51:16.205|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-05 11:51:16.210|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 11:51:16.249|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-06-05 11:51:16.440|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-05 11:51:16.442|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-05 11:51:16.739|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=051c69fb-cc0a-3bce-8818-896f0515bc35
2025-06-05 11:51:16.829|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-05 11:51:16.833|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-05 11:51:25.062|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:51:29.087|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:51:33.141|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:51:37.182|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:51:37.182|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-05 11:51:41.232|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:51:45.280|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:51:45.280|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-05 11:51:45.329|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-05 11:51:45.819|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 11:51:45.824|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 11:51:45.853|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 11:51:45.989|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$5ef5d953] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 11:51:46.010|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 11:51:46.011|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 11:51:46.014|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 11:51:49.309|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:51:53.336|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:51:57.338|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:52:01.340|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:52:05.342|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:52:05.373|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-05 11:52:06.809|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-05 11:52:06.810|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-05 11:52:06.810|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-05 11:52:06.810|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-05 11:52:06.811|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-05 11:52:06.811|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-05 11:52:06.811|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-05 11:52:06.811|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-05 11:52:06.811|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-05 11:52:06.811|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-05 11:52:06.812|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-05 11:52:06.812|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-05 11:52:06.812|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-05 11:52:07.005|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 11:52:07.096|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 11:52:07.162|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 11:52:07.202|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 11:52:07.228|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 11:52:07.258|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 11:52:07.350|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 11:52:07.351|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 11:52:07.478|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 11:52:07.478|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 11:52:07.933|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-05 11:52:07.935|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-05 11:52:08.132|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-05 11:52:08.305|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-05 11:52:08.453|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-05 11:52:08.987|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-05 11:52:09.161|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-05 11:52:09.343|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:52:09.693|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-05 11:52:09.976|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.306 seconds (JVM running for 59.766)
2025-06-05 11:52:09.977|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-05 11:52:09.977|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-05 11:52:09.981|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-05 11:52:10.045|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-05 11:53:42.205|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:54:16.899|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-05 11:54:16.912|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 28664 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-05 11:54:16.913|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-05 11:54:18.294|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-05 11:54:18.298|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 11:54:18.335|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-06-05 11:54:18.529|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-05 11:54:18.532|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-05 11:54:18.903|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=f1834702-be98-392b-a740-3eb50a7d7490
2025-06-05 11:54:18.978|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-05 11:54:18.990|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-05 11:54:27.239|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:54:31.280|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:54:35.318|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:54:39.357|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:54:39.357|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-05 11:54:43.398|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:54:47.450|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:54:47.450|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-05 11:54:47.498|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-05 11:54:47.935|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 11:54:47.939|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 11:54:47.974|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 11:54:48.106|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$1bef3e00] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 11:54:48.125|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 11:54:48.126|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 11:54:48.129|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 11:54:51.488|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:54:55.491|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:54:59.494|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:55:03.496|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:55:07.499|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:55:07.530|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-05 11:55:08.899|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-05 11:55:08.899|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-05 11:55:08.899|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-05 11:55:08.899|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-05 11:55:08.899|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-05 11:55:08.900|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-05 11:55:08.900|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-05 11:55:08.900|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-05 11:55:08.900|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-05 11:55:08.900|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-05 11:55:08.900|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-05 11:55:08.901|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-05 11:55:08.901|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-05 11:55:09.066|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 11:55:09.140|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 11:55:09.187|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 11:55:09.220|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 11:55:09.237|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 11:55:09.262|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 11:55:09.343|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 11:55:09.344|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 11:55:09.356|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 11:55:09.357|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 11:55:09.942|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-05 11:55:09.945|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-05 11:55:10.109|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-05 11:55:10.269|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-05 11:55:10.389|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-05 11:55:10.879|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-05 11:55:11.044|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-05 11:55:11.501|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 11:55:11.533|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-05 11:55:11.794|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.106 seconds (JVM running for 58.881)
2025-06-05 11:55:11.795|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-05 11:55:11.796|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-05 11:55:11.799|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-05 11:55:11.856|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-05 11:55:36.606|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:32:45.514|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-05 13:32:45.531|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 15892 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-05 13:32:45.531|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-05 13:32:46.844|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-05 13:32:46.848|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 13:32:46.885|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-06-05 13:32:47.069|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-05 13:32:47.071|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-05 13:32:47.458|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=051c69fb-cc0a-3bce-8818-896f0515bc35
2025-06-05 13:32:47.548|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-05 13:32:47.552|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-05 13:32:55.766|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:32:59.821|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:33:03.858|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:33:07.900|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:33:07.900|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-05 13:33:11.944|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:33:15.991|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:33:15.991|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-05 13:33:16.037|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-05 13:33:16.513|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 13:33:16.518|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 13:33:16.546|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 13:33:16.709|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$c5138fe5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 13:33:16.729|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 13:33:16.731|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 13:33:16.733|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 13:33:20.028|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:33:24.040|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:33:28.043|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:33:32.044|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:33:36.046|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:33:36.076|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-05 13:33:37.454|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-05 13:33:37.454|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-05 13:33:37.463|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-05 13:33:37.463|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-05 13:33:37.463|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-05 13:33:37.464|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-05 13:33:37.464|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-05 13:33:37.464|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-05 13:33:37.464|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-05 13:33:37.464|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-05 13:33:37.465|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-05 13:33:37.465|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-05 13:33:37.465|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-05 13:33:37.626|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 13:33:37.706|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 13:33:37.751|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 13:33:37.789|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 13:33:37.809|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 13:33:37.840|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 13:33:37.938|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 13:33:37.938|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 13:33:37.950|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 13:33:37.950|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 13:33:38.634|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-05 13:33:38.636|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-05 13:33:38.876|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-05 13:33:39.054|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-05 13:33:39.173|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-05 13:33:39.672|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-05 13:33:39.843|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-05 13:33:40.125|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:33:40.323|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-05 13:33:40.584|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.256 seconds (JVM running for 59.199)
2025-06-05 13:33:40.585|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-05 13:33:40.585|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-05 13:33:40.589|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-05 13:33:40.649|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-05 13:34:00.127|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:34:36.129|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 64 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:35:44.133|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:38:57.208|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:40:13.331|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:40:19.055|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:41:26.807|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:42:59.769|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:44:54.669|ERROR| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:187|权限校验过程中发生异常
reactor.blockhound.BlockingOperationError: Blocking call! sun.misc.Unsafe#park
	at sun.misc.Unsafe.park(Unsafe.java)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.MonoFromPublisher] :
	reactor.core.publisher.Mono.from(Mono.java:513)
	io.lettuce.core.AbstractRedisReactiveCommands.createMono(AbstractRedisReactiveCommands.java:458)
Error has been observed at the following site(s):
	|_                       Mono.from ⇢ at io.lettuce.core.AbstractRedisReactiveCommands.createMono(AbstractRedisReactiveCommands.java:458)
	|_                        Mono.map ⇢ at org.springframework.data.redis.connection.lettuce.LettuceReactiveSetCommands.lambda$null$17(LettuceReactiveSetCommands.java:162)
	|_                  Flux.concatMap ⇢ at org.springframework.data.redis.connection.lettuce.LettuceReactiveSetCommands.lambda$sIsMember$18(LettuceReactiveSetCommands.java:157)
	|_                Mono.flatMapMany ⇢ at org.springframework.data.redis.connection.lettuce.LettuceReactiveRedisConnection.execute(LettuceReactiveRedisConnection.java:224)
	|_ MonoNext$NextSubscriber.onError ⇢ at io.lettuce.core.RedisPublisher$ImmediateSubscriber.onError(RedisPublisher.java:891)
	|_                 Flux.onErrorMap ⇢ at org.springframework.data.redis.connection.lettuce.LettuceReactiveRedisConnection.execute(LettuceReactiveRedisConnection.java:224)
	|_                       Flux.next ⇢ at org.springframework.data.redis.connection.ReactiveSetCommands.sIsMember(ReactiveSetCommands.java:545)
	|_                        Mono.map ⇢ at org.springframework.data.redis.connection.ReactiveSetCommands.sIsMember(ReactiveSetCommands.java:545)
	|_                  Flux.usingWhen ⇢ at org.springframework.data.redis.core.ReactiveRedisTemplate.doInConnection(ReactiveRedisTemplate.java:195)
	|_                       Mono.from ⇢ at org.springframework.data.redis.core.ReactiveRedisTemplate.createMono(ReactiveRedisTemplate.java:179)
	|_                Mono.subscribeOn ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.lambda$filter$4(AccessGatewayFilter.java:156)
	|_                    Mono.flatMap ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.lambda$filter$4(AccessGatewayFilter.java:157)
	|_                    Mono.flatMap ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.filter(AccessGatewayFilter.java:132)
Stack trace:
		at sun.misc.Unsafe.park(Unsafe.java)
		at java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
		at java.util.concurrent.locks.AbstractQueuedSynchronizer.parkAndCheckInterrupt(AbstractQueuedSynchronizer.java:836)
		at java.util.concurrent.locks.AbstractQueuedSynchronizer.acquireQueued(AbstractQueuedSynchronizer.java:870)
		at java.util.concurrent.locks.AbstractQueuedSynchronizer.acquire(AbstractQueuedSynchronizer.java:1199)
		at java.util.concurrent.locks.ReentrantLock$NonfairSync.lock(ReentrantLock.java:209)
		at java.util.concurrent.locks.ReentrantLock.lock(ReentrantLock.java:285)
		at java.util.concurrent.LinkedBlockingQueue.signalNotEmpty(LinkedBlockingQueue.java:172)
		at java.util.concurrent.LinkedBlockingQueue.offer(LinkedBlockingQueue.java:430)
		at java.util.AbstractQueue.add(AbstractQueue.java:95)
		at org.LatencyUtils.PauseDetector.addListener(PauseDetector.java:57)
		at org.LatencyUtils.TimeCappedMovingAverageIntervalEstimator$PauseTracker.<init>(TimeCappedMovingAverageIntervalEstimator.java:283)
		at org.LatencyUtils.TimeCappedMovingAverageIntervalEstimator.<init>(TimeCappedMovingAverageIntervalEstimator.java:71)
		at org.LatencyUtils.LatencyStats.<init>(LatencyStats.java:181)
		at org.LatencyUtils.LatencyStats$Builder.build(LatencyStats.java:390)
		at io.lettuce.core.metrics.DefaultCommandLatencyCollector$Latencies.<init>(DefaultCommandLatencyCollector.java:271)
		at io.lettuce.core.metrics.DefaultCommandLatencyCollector.lambda$recordCommandLatency$0(DefaultCommandLatencyCollector.java:114)
		at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
		at io.lettuce.core.metrics.DefaultCommandLatencyCollector.recordCommandLatency(DefaultCommandLatencyCollector.java:111)
		at io.lettuce.core.protocol.CommandHandler.recordLatency(CommandHandler.java:910)
		at io.lettuce.core.protocol.CommandHandler.decode(CommandHandler.java:766)
		at io.lettuce.core.protocol.CommandHandler.decode(CommandHandler.java:654)
		at io.lettuce.core.protocol.CommandHandler.channelRead(CommandHandler.java:594)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
2025-06-05 13:44:57.624|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:45:01.627|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:47:01.626|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:49:36.645|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-05 13:49:36.659|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 26720 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-05 13:49:36.660|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-05 13:49:38.086|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-05 13:49:38.090|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 13:49:38.130|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-06-05 13:49:38.304|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-05 13:49:38.307|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-05 13:49:38.685|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=051c69fb-cc0a-3bce-8818-896f0515bc35
2025-06-05 13:49:38.799|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-05 13:49:38.802|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-05 13:49:47.046|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:49:51.092|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:49:55.134|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:49:59.175|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:49:59.175|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-05 13:50:03.222|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:50:07.258|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:50:07.258|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-05 13:50:07.311|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-05 13:50:07.809|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 13:50:07.814|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 13:50:07.845|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 13:50:08.002|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$1bef3e00] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 13:50:08.028|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 13:50:08.030|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 13:50:08.033|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 13:50:11.300|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:50:15.321|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:50:19.324|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:50:23.326|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:50:27.328|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:50:27.359|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-05 13:50:28.780|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-05 13:50:28.781|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-05 13:50:28.781|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-05 13:50:28.781|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-05 13:50:28.781|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-05 13:50:28.781|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-05 13:50:28.781|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-05 13:50:28.782|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-05 13:50:28.782|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-05 13:50:28.782|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-05 13:50:28.782|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-05 13:50:28.783|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-05 13:50:28.783|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-05 13:50:28.942|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 13:50:29.009|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 13:50:29.050|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 13:50:29.082|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 13:50:29.099|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 13:50:29.125|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 13:50:29.230|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 13:50:29.230|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 13:50:29.243|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 13:50:29.243|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 13:50:29.839|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-05 13:50:29.842|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-05 13:50:30.022|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-05 13:50:30.222|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-05 13:50:30.354|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-05 13:50:30.878|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-05 13:50:31.057|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-05 13:50:31.378|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:50:31.542|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-05 13:50:31.846|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.532 seconds (JVM running for 59.338)
2025-06-05 13:50:31.847|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-05 13:50:31.847|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-05 13:50:31.851|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-05 13:50:31.915|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-05 13:50:51.379|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:52:33.230|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 64 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:53:41.232|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:54:51.051|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:55:19.323|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:55:45.235|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:57:49.237|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:59:51.050|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 13:59:55.052|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:00:19.324|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:01:59.055|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:03:03.358|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-05 14:03:03.415|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 28740 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-05 14:03:03.415|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-05 14:03:04.789|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-05 14:03:04.793|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 14:03:04.846|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
2025-06-05 14:03:05.030|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-05 14:03:05.032|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-05 14:03:05.296|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=051c69fb-cc0a-3bce-8818-896f0515bc35
2025-06-05 14:03:05.384|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-05 14:03:05.388|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-05 14:03:13.631|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:03:17.694|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:03:21.724|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:03:25.769|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:03:25.769|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-05 14:03:29.812|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:03:33.858|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:03:33.858|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-05 14:03:33.904|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-05 14:03:34.374|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:03:34.380|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:03:34.411|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:03:34.540|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$4628d629] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:03:34.558|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:03:34.559|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:03:34.561|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:03:37.902|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:03:41.905|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:03:45.906|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:03:49.908|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:03:53.910|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:03:53.942|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-05 14:03:55.326|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-05 14:03:55.326|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-05 14:03:55.326|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-05 14:03:55.327|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-05 14:03:55.327|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-05 14:03:55.327|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-05 14:03:55.327|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-05 14:03:55.327|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-05 14:03:55.328|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-05 14:03:55.328|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-05 14:03:55.328|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-05 14:03:55.328|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-05 14:03:55.328|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-05 14:03:55.490|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:03:55.553|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:03:55.592|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:03:55.623|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:03:55.642|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:03:55.666|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:03:55.742|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 14:03:55.742|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 14:03:55.752|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 14:03:55.752|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 14:03:56.320|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-05 14:03:56.324|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-05 14:03:56.496|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-05 14:03:56.647|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-05 14:03:56.765|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-05 14:03:57.273|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-05 14:03:57.448|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-05 14:03:57.915|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:03:57.928|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-05 14:03:58.204|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.068 seconds (JVM running for 58.768)
2025-06-05 14:03:58.205|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-05 14:03:58.206|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-05 14:03:58.209|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-05 14:03:58.270|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-05 14:04:17.918|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:08:42.926|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-05 14:08:42.949|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 27192 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-05 14:08:42.949|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-05 14:08:44.423|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-05 14:08:44.428|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 14:08:44.472|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 26 ms. Found 0 Redis repository interfaces.
2025-06-05 14:08:44.709|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-05 14:08:44.712|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-05 14:08:45.020|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=051c69fb-cc0a-3bce-8818-896f0515bc35
2025-06-05 14:08:45.111|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-05 14:08:45.115|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-05 14:08:53.367|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:08:57.410|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:09:01.445|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:09:05.478|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:09:05.478|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-05 14:09:09.515|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:09:13.548|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:09:13.549|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-05 14:09:13.604|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-05 14:09:14.056|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:09:14.060|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:09:14.093|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:09:14.216|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$17fa56c6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:09:14.237|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:09:14.238|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:09:14.240|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:09:17.593|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:09:21.619|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:09:25.620|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:09:29.623|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:09:33.625|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:09:33.657|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-05 14:09:35.071|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-05 14:09:35.071|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-05 14:09:35.072|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-05 14:09:35.072|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-05 14:09:35.072|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-05 14:09:35.072|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-05 14:09:35.072|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-05 14:09:35.073|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-05 14:09:35.073|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-05 14:09:35.073|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-05 14:09:35.073|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-05 14:09:35.074|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-05 14:09:35.074|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-05 14:09:35.247|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:09:35.312|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:09:35.357|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:09:35.391|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:09:35.409|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:09:35.439|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:09:35.528|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 14:09:35.528|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 14:09:35.539|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 14:09:35.539|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 14:09:36.141|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-05 14:09:36.143|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-05 14:09:36.330|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-05 14:09:36.494|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-05 14:09:36.619|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-05 14:09:37.143|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-05 14:09:37.347|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-05 14:09:37.676|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:09:37.909|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-05 14:09:38.199|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.695 seconds (JVM running for 59.696)
2025-06-05 14:09:38.200|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-05 14:09:38.200|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-05 14:09:38.204|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-05 14:09:38.279|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-05 14:10:04.147|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:10:36.352|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-05 14:10:36.369|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 27240 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-05 14:10:36.369|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-05 14:10:37.847|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-05 14:10:37.851|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 14:10:37.891|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-06-05 14:10:38.088|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-05 14:10:38.091|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-05 14:10:38.441|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=051c69fb-cc0a-3bce-8818-896f0515bc35
2025-06-05 14:10:38.516|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-05 14:10:38.520|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-05 14:10:46.749|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:10:50.788|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:10:54.827|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:10:58.861|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:10:58.862|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-05 14:11:02.909|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:11:06.945|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:11:06.945|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-05 14:11:06.996|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-05 14:11:07.471|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:11:07.477|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:11:07.507|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:11:07.651|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$a852b6e5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:11:07.672|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:11:07.673|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:11:07.675|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:11:10.989|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:11:15.014|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:11:19.015|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:11:23.018|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:11:27.020|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:11:27.053|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-05 14:11:28.495|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-05 14:11:28.495|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-05 14:11:28.495|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-05 14:11:28.495|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-05 14:11:28.495|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-05 14:11:28.496|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-05 14:11:28.496|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-05 14:11:28.496|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-05 14:11:28.496|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-05 14:11:28.497|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-05 14:11:28.497|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-05 14:11:28.497|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-05 14:11:28.497|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-05 14:11:28.665|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:11:28.740|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:11:28.782|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:11:28.817|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:11:28.837|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:11:28.864|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:11:28.957|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 14:11:28.957|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 14:11:29.091|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 14:11:29.092|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 14:11:29.548|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-05 14:11:29.551|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-05 14:11:29.734|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-05 14:11:29.896|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-05 14:11:30.012|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-05 14:11:30.529|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-05 14:11:30.699|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-05 14:11:31.084|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:11:31.184|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-05 14:11:31.458|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.319 seconds (JVM running for 59.079)
2025-06-05 14:11:31.459|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-05 14:11:31.459|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-05 14:11:31.463|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-05 14:11:31.528|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-05 14:11:51.085|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:12:38.956|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 64 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:14:05.632|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:15:25.962|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-05 14:15:25.985|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 30676 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-05 14:15:25.986|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-05 14:15:27.443|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-05 14:15:27.447|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 14:15:27.489|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-06-05 14:15:27.705|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-05 14:15:27.707|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-05 14:15:28.097|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=051c69fb-cc0a-3bce-8818-896f0515bc35
2025-06-05 14:15:28.178|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-05 14:15:28.181|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-05 14:15:36.461|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:15:40.493|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:15:44.541|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:15:48.573|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:15:48.574|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-05 14:15:52.610|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:15:56.642|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:15:56.642|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-05 14:15:56.689|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-05 14:15:57.123|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:15:57.127|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:15:57.155|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:15:57.290|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$82a9e886] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:15:57.308|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:15:57.310|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:15:57.312|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:16:00.684|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:16:04.687|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:16:08.689|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:16:12.690|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:16:16.693|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:16:16.724|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-05 14:16:18.100|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-05 14:16:18.100|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-05 14:16:18.101|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-05 14:16:18.101|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-05 14:16:18.101|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-05 14:16:18.101|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-05 14:16:18.101|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-05 14:16:18.101|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-05 14:16:18.101|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-05 14:16:18.101|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-05 14:16:18.102|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-05 14:16:18.102|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-05 14:16:18.102|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-05 14:16:18.251|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:16:18.332|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:16:18.372|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:16:18.402|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:16:18.420|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:16:18.443|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:16:18.526|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 14:16:18.526|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 14:16:18.664|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 14:16:18.664|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 14:16:19.100|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-05 14:16:19.103|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-05 14:16:19.280|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-05 14:16:19.433|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-05 14:16:19.553|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-05 14:16:20.057|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-05 14:16:20.233|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-05 14:16:20.696|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:16:20.715|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-05 14:16:20.978|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.295 seconds (JVM running for 59.583)
2025-06-05 14:16:20.979|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-05 14:16:20.979|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-05 14:16:20.983|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-05 14:16:21.046|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-05 14:16:54.346|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:21:15.107|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-05 14:21:15.115|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 13376 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-05 14:21:15.115|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-05 14:21:16.502|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-05 14:21:16.506|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 14:21:16.558|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 35 ms. Found 0 Redis repository interfaces.
2025-06-05 14:21:16.750|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-05 14:21:16.752|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-05 14:21:17.065|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=051c69fb-cc0a-3bce-8818-896f0515bc35
2025-06-05 14:21:17.147|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-05 14:21:17.151|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-05 14:21:25.391|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:21:29.446|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:21:33.484|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:21:37.522|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:21:37.523|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-05 14:21:41.568|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:21:45.613|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:21:45.613|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-05 14:21:45.661|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-05 14:21:46.127|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:21:46.131|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:21:46.160|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:21:46.296|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$bb5d2d3d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:21:46.317|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:21:46.318|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:21:46.320|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:21:49.643|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:21:53.662|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:21:57.664|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:22:01.665|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:22:05.667|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:22:05.698|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-05 14:22:07.109|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-05 14:22:07.110|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-05 14:22:07.110|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-05 14:22:07.110|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-05 14:22:07.110|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-05 14:22:07.110|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-05 14:22:07.111|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-05 14:22:07.111|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-05 14:22:07.111|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-05 14:22:07.111|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-05 14:22:07.111|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-05 14:22:07.111|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-05 14:22:07.112|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-05 14:22:07.265|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:22:07.336|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:22:07.376|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:22:07.407|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:22:07.425|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:22:07.451|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:22:07.534|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 14:22:07.534|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 14:22:07.544|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 14:22:07.545|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 14:22:08.124|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-05 14:22:08.126|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-05 14:22:08.303|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-05 14:22:08.462|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-05 14:22:08.582|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-05 14:22:09.095|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-05 14:22:09.274|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-05 14:22:09.670|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:22:09.783|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-05 14:22:10.047|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.206 seconds (JVM running for 58.825)
2025-06-05 14:22:10.048|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-05 14:22:10.051|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-05 14:22:10.055|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-05 14:22:10.118|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-05 14:22:29.671|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:22:57.388|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-05 14:22:57.400|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 30244 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-05 14:22:57.400|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-05 14:22:58.794|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-05 14:22:58.798|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 14:22:58.844|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
2025-06-05 14:22:59.019|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-05 14:22:59.021|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-05 14:22:59.375|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=051c69fb-cc0a-3bce-8818-896f0515bc35
2025-06-05 14:22:59.448|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-05 14:22:59.451|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-05 14:23:07.712|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:23:11.756|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:23:15.805|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:23:19.848|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:23:19.848|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-05 14:23:23.875|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:23:27.920|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:23:27.920|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-05 14:23:27.966|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-05 14:23:28.405|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:23:28.410|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:23:28.438|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:23:28.559|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$e7957146] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:23:28.578|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:23:28.579|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:23:28.582|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:23:31.959|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:23:35.984|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:23:39.986|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:23:43.989|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:23:47.992|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:23:48.020|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-05 14:23:49.372|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-05 14:23:49.372|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-05 14:23:49.381|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-05 14:23:49.381|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-05 14:23:49.382|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-05 14:23:49.382|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-05 14:23:49.382|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-05 14:23:49.382|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-05 14:23:49.382|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-05 14:23:49.382|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-05 14:23:49.383|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-05 14:23:49.383|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-05 14:23:49.383|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-05 14:23:49.537|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:23:49.596|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:23:49.632|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:23:49.660|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:23:49.677|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:23:49.706|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:23:49.781|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 14:23:49.781|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 14:23:49.789|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 14:23:49.790|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 14:23:50.349|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-05 14:23:50.352|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-05 14:23:50.523|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-05 14:23:50.672|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-05 14:23:50.794|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-05 14:23:51.305|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-05 14:23:51.481|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-05 14:23:51.960|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-05 14:23:51.995|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:23:52.228|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.173 seconds (JVM running for 59.061)
2025-06-05 14:23:52.229|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-05 14:23:52.229|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-05 14:23:52.233|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-05 14:23:52.299|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-05 14:24:11.998|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:24:48.008|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 64 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:25:56.010|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:28:00.014|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:28:11.730|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:28:39.989|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:30:04.017|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:32:08.020|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:33:11.725|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:33:39.989|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:34:17.932|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-05 14:34:17.950|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 18336 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-05 14:34:17.951|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-05 14:34:19.314|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-05 14:34:19.320|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 14:34:19.358|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-06-05 14:34:19.550|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-05 14:34:19.552|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-05 14:34:19.847|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=051c69fb-cc0a-3bce-8818-896f0515bc35
2025-06-05 14:34:19.929|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-05 14:34:19.933|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-05 14:34:28.126|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:34:32.176|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:34:36.223|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:34:40.261|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:34:40.262|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-05 14:34:44.310|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:34:48.338|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:34:48.338|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-05 14:34:48.385|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-05 14:34:48.942|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:34:48.947|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:34:48.978|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:34:49.097|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$c5138fe5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:34:49.115|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:34:49.117|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:34:49.119|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:34:52.383|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:34:56.410|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:35:00.412|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:35:04.414|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:35:08.416|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:35:08.451|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-05 14:35:10.395|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-05 14:35:10.395|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-05 14:35:10.396|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-05 14:35:10.396|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-05 14:35:10.396|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-05 14:35:10.396|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-05 14:35:10.396|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-05 14:35:10.396|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-05 14:35:10.396|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-05 14:35:10.396|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-05 14:35:10.397|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-05 14:35:10.397|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-05 14:35:10.397|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-05 14:35:10.558|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:35:10.641|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:35:10.680|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:35:10.715|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:35:10.732|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:35:10.764|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:35:10.864|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 14:35:10.865|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 14:35:10.879|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 14:35:10.880|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 14:35:11.500|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-05 14:35:11.506|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-05 14:35:11.723|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-05 14:35:11.907|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-05 14:35:12.037|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-05 14:35:12.418|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:35:12.544|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-05 14:35:12.741|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-05 14:35:13.277|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-05 14:35:13.545|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.825 seconds (JVM running for 59.576)
2025-06-05 14:35:13.546|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-05 14:35:13.546|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-05 14:35:13.549|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-05 14:35:13.607|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-05 14:35:37.714|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:36:39.149|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 64 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:38:14.580|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:39:43.350|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:40:23.571|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:40:27.574|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:42:31.499|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:43:51.115|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 2448 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-05 14:43:51.116|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-05 14:43:51.136|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-05 14:43:52.521|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-05 14:43:52.525|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 14:43:52.564|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-06-05 14:43:52.767|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-05 14:43:52.769|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-05 14:43:53.175|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=be0cfe6b-1700-3bc5-a300-049a5675ac02
2025-06-05 14:43:53.248|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-05 14:43:53.251|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-05 14:44:01.495|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:44:05.541|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:44:09.596|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:44:13.640|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:44:13.640|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-05 14:44:17.693|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:44:21.731|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:44:21.731|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-05 14:44:21.795|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-05 14:44:22.291|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:44:22.296|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:44:22.328|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:44:22.503|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$504fbdb6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:44:22.528|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:44:22.530|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:44:22.533|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:44:25.780|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:44:29.802|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:44:33.804|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:44:37.807|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:44:41.809|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:44:41.838|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-05 14:44:42.361|WARN| N/A||main|o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'yxtLoginAccessGatewayFilterFactory': Unsatisfied dependency expressed through field 'redisTemplate'; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.springframework.data.redis.core.RedisTemplate<java.lang.String, java.lang.Object>' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
2025-06-05 14:44:42.439|INFO| N/A||main|o.s.b.a.l.ConditionEvaluationReportLoggingListener:136|

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-05 14:44:42.476|ERROR| N/A||main|o.s.b.d.LoggingFailureAnalysisReporter:40|

***************************
APPLICATION FAILED TO START
***************************

Description:

Field redisTemplate in cn.hydee.gateway.filter.YxtLoginAccessGatewayFilterFactory required a bean of type 'org.springframework.data.redis.core.RedisTemplate' that could not be found.

The injection point has the following annotations:
	- @org.springframework.beans.factory.annotation.Autowired(required=true)


Action:

Consider defining a bean of type 'org.springframework.data.redis.core.RedisTemplate' in your configuration.

2025-06-05 14:44:45.810|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:45:05.813|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:45:41.815|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 64 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:46:49.819|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:48:39.065|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-05 14:48:39.100|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 13352 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-05 14:48:39.101|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-05 14:48:40.480|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-05 14:48:40.486|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 14:48:40.537|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 27 ms. Found 0 Redis repository interfaces.
2025-06-05 14:48:40.730|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-05 14:48:40.733|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-05 14:48:41.029|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=051c69fb-cc0a-3bce-8818-896f0515bc35
2025-06-05 14:48:41.131|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-05 14:48:41.135|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-05 14:48:49.388|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:48:53.446|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:48:57.498|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:49:01.525|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:49:01.526|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-05 14:49:05.565|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:49:09.600|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:49:09.600|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-05 14:49:09.680|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-05 14:49:10.137|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:49:10.142|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:49:10.169|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:49:10.297|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$cffa34b5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:49:10.316|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:49:10.317|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:49:10.319|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 14:49:13.647|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:49:17.674|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:49:21.676|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:49:25.679|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:49:29.681|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:49:29.711|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-05 14:49:31.137|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-05 14:49:31.137|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-05 14:49:31.137|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-05 14:49:31.138|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-05 14:49:31.138|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-05 14:49:31.138|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-05 14:49:31.138|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-05 14:49:31.138|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-05 14:49:31.138|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-05 14:49:31.139|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-05 14:49:31.139|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-05 14:49:31.139|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-05 14:49:31.139|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-05 14:49:31.292|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:49:31.351|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:49:31.402|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:49:31.432|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:49:31.449|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:49:31.476|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 14:49:31.565|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 14:49:31.565|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 14:49:31.578|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 14:49:31.578|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 14:49:32.160|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-05 14:49:32.162|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-05 14:49:32.343|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-05 14:49:32.501|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-05 14:49:32.617|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-05 14:49:33.131|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-05 14:49:33.307|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-05 14:49:33.710|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:49:33.809|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-05 14:49:34.074|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.186 seconds (JVM running for 59.321)
2025-06-05 14:49:34.075|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-05 14:49:34.075|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-05 14:49:34.079|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-05 14:49:34.143|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-05 14:49:53.713|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:50:29.715|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 64 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:51:37.718|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:53:41.720|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:53:53.396|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:54:21.678|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:55:45.723|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:57:49.725|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:58:53.399|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 14:59:21.679|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:04:32.705|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-05 15:04:32.718|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 27412 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-05 15:04:32.718|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-05 15:04:34.066|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-05 15:04:34.070|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 15:04:34.107|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-06-05 15:04:34.304|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-05 15:04:34.307|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-05 15:04:34.599|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=051c69fb-cc0a-3bce-8818-896f0515bc35
2025-06-05 15:04:34.689|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-05 15:04:34.693|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-05 15:04:42.921|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:04:46.969|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:04:51.010|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:04:55.057|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:04:55.057|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-05 15:04:59.107|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:05:03.140|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:05:03.140|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-05 15:05:03.193|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-05 15:05:03.657|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 15:05:03.662|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 15:05:03.692|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 15:05:03.885|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$f63c5c49] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 15:05:03.911|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 15:05:03.912|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 15:05:03.915|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 15:05:07.185|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:05:11.210|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:05:15.212|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:05:19.214|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:05:23.216|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:05:23.248|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-05 15:05:24.625|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-05 15:05:24.625|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-05 15:05:24.626|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-05 15:05:24.626|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-05 15:05:24.626|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-05 15:05:24.626|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-05 15:05:24.627|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-05 15:05:24.627|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-05 15:05:24.627|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-05 15:05:24.627|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-05 15:05:24.627|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-05 15:05:24.627|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-05 15:05:24.628|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-05 15:05:24.793|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 15:05:24.855|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 15:05:24.896|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 15:05:24.929|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 15:05:24.948|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 15:05:24.971|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 15:05:25.047|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 15:05:25.047|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 15:05:25.056|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 15:05:25.056|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 15:05:25.621|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-05 15:05:25.624|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-05 15:05:25.799|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-05 15:05:25.961|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-05 15:05:26.077|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-05 15:05:26.653|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-05 15:05:26.825|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-05 15:05:27.218|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:05:27.332|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-05 15:05:27.596|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.112 seconds (JVM running for 59.062)
2025-06-05 15:05:27.597|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-05 15:05:27.597|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-05 15:05:27.602|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-05 15:05:27.664|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-05 15:05:56.652|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:09:31.436|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-05 15:09:31.457|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 28972 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-05 15:09:31.458|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-05 15:09:32.941|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-05 15:09:32.946|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 15:09:32.982|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-06-05 15:09:33.187|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-05 15:09:33.189|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-05 15:09:33.510|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=051c69fb-cc0a-3bce-8818-896f0515bc35
2025-06-05 15:09:33.591|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-05 15:09:33.594|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-05 15:09:41.824|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:09:45.855|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:09:49.893|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:09:53.928|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:09:53.928|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-05 15:09:57.975|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:10:02.018|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:10:02.018|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-05 15:10:02.069|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-05 15:10:02.498|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 15:10:02.503|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 15:10:02.532|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 15:10:02.669|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$94bd8931] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 15:10:02.694|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 15:10:02.696|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 15:10:02.698|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 15:10:06.061|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:10:10.084|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:10:14.086|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:10:18.087|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:10:22.090|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:10:22.121|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-05 15:10:23.447|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-05 15:10:23.447|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-05 15:10:23.448|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-05 15:10:23.448|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-05 15:10:23.448|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-05 15:10:23.448|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-05 15:10:23.448|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-05 15:10:23.448|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-05 15:10:23.448|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-05 15:10:23.449|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-05 15:10:23.449|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-05 15:10:23.449|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-05 15:10:23.449|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-05 15:10:23.606|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 15:10:23.673|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 15:10:23.710|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 15:10:23.742|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 15:10:23.762|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 15:10:23.787|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-05 15:10:23.866|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 15:10:23.866|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 15:10:24.016|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-05 15:10:24.016|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-05 15:10:24.467|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-05 15:10:24.470|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-05 15:10:24.642|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-05 15:10:24.791|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-05 15:10:24.903|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-05 15:10:25.415|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-05 15:10:25.599|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-05 15:10:26.091|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:10:26.099|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-05 15:10:26.364|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.157 seconds (JVM running for 59.038)
2025-06-05 15:10:26.365|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-05 15:10:26.365|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-05 15:10:26.369|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-05 15:10:26.436|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-05 15:10:46.094|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:11:22.095|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 64 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-05 15:12:30.099|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
