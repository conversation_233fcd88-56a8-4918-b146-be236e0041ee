//package com.hydee.gateway.feign;
//
//import com.honey.common.util.SpringUtils;
//import com.hydee.gateway.yxtadapter.domain.xy.getorderinfo.KcPosGetOrderDetailCallBackRequest;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.cloud.openfeign.support.FeignUtils;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//
///**
// * <AUTHOR> (moatkon)
// * @date 2024年01月15日 16:45
// * @email: <EMAIL>
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class KcPosCallBackClientTest {
//
//    @Resource
//    private KcPosCallBackClient kcPosCallBackClient;
//    @Resource
//    private OrderBypassIsNewClient orderBypassIsNewClient;
//
//
//    @Test
//    public void testKcPosCallBackClient(){
//        String orderDetail = kcPosCallBackClient.getOrderDetail(new KcPosGetOrderDetailCallBackRequest());
//        System.out.println();
//    }
//    @Test
//    public void testOrderBypassIsNewClient(){
//        orderBypassIsNewClient.isBypassByOldPosToken("");
//        System.out.println();
//    }
//
//
//}
