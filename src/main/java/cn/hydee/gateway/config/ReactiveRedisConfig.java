package cn.hydee.gateway.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.ReactiveRedisConnectionFactory;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;
/**
 * 响应式redis项配置
 */
@Component
public class ReactiveRedisConfig {


  /**
   * 异步非阻塞的RedisTemplate - String类型
   *
   * @param reactiveRedisConnectionFactory
   * @return
   */
  @Bean
  @Primary
  public ReactiveRedisTemplate<String, String> reactiveRedisTemplate(
      ReactiveRedisConnectionFactory reactiveRedisConnectionFactory) {
    StringRedisSerializer serializer = new StringRedisSerializer();
    RedisSerializationContext<String, String> context = RedisSerializationContext.<String, String>newSerializationContext()
        .key(serializer)
        .value(serializer)  // 使用字符串序列化器
        .hashKey(serializer)
        .hashValue(serializer)
        .build();
    return new ReactiveRedisTemplate<>(reactiveRedisConnectionFactory, context);
  }


  /**
   * 异步非阻塞的RedisTemplate - Object类型
   *
   * @param reactiveRedisConnectionFactory
   * @return
   */
  @Bean("reactiveRedisObjectTemplate")
  public ReactiveRedisTemplate<String, Object> reactiveRedisObjectTemplate(
      ReactiveRedisConnectionFactory reactiveRedisConnectionFactory) {
    Jackson2JsonRedisSerializer<Object> obj = new Jackson2JsonRedisSerializer<>(
        Object.class);

    Jackson2JsonRedisSerializer<String> str = new Jackson2JsonRedisSerializer<>(
        String.class);

    ObjectMapper om = new ObjectMapper();
    om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
    om.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
    obj.setObjectMapper(om);

    RedisSerializationContext<String, Object> context = RedisSerializationContext.<String, Object>newSerializationContext()
        .key(str)
        .value(obj)
        .hashKey(str)
        .hashValue(obj)
        .build();
    return new ReactiveRedisTemplate<>(reactiveRedisConnectionFactory, context);
  }

}