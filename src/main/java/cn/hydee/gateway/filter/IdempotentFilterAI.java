package cn.hydee.gateway.filter;

import static cn.hydee.gateway.filter.AccessGatewayFilter.HEAD_USER_ID_KEY;

import cn.hydee.gateway.config.IdempotentConfig;
import cn.hydee.gateway.constants.RestCodeConstants;
import cn.hydee.gateway.domain.ReturnData;
import cn.hydee.gateway.util.Const;
import com.alibaba.fastjson.JSONObject;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 防重提交过滤器 - 使用响应式Redis操作
 *
 * <p>该过滤器用于防止重复提交，通过Redis计数器实现幂等性控制。
 * 使用ReactiveRedisTemplate保持响应式编程模式，避免阻塞操作。</p>
 *
 * <AUTHOR>
 * @version 2.0 - 重构为响应式实现
 * @date 2020/2/18 13:50
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(IdempotentConfig.class)
public class IdempotentFilterAI implements GlobalFilter, Ordered {

  /**
   * 防重提交配置
   */
  @Autowired
  private IdempotentConfig idempotentConfig;

  /**
   * 响应式Redis模板，用于执行非阻塞的Redis操作
   */
  @Resource
  private ReactiveRedisTemplate<String, String> reactiveRedisTemplate;


  /**
   * 过滤器核心逻辑 - 响应式防重提交实现
   *
   * @param exchange 服务器Web交换对象
   * @param chain    过滤器链
   * @return 响应式结果
   */
  @Override
  public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
    ServerHttpRequest request = exchange.getRequest();
    String requestUri = request.getPath().pathWithinApplication().value();
    String method = request.getMethodValue();

    // 检查是否需要进行防重提交验证
    if (!idempotentConfig.isContainWith(method, requestUri)) {
      // 不在配置范围内，直接放行
      return chain.filter(exchange);
    }

    // 从请求头中获取用户ID
    String userId = request.getHeaders().getFirst(HEAD_USER_ID_KEY);

    // 构建Redis键名
    String key = buildIncrementKey(userId, method, requestUri);

    // 使用响应式Redis操作进行防重提交检查
    return reactiveRedisTemplate.opsForValue()
        .increment(key)  // 原子性递增操作
        .flatMap(count -> {
          // 如果计数大于1，说明是重复请求
          if (count != null && count > 1) {
            log.warn("检测到重复请求 - 用户ID: {}, 方法: {}, URI: {}, 计数: {}",
                userId, method, requestUri, count);

            // 构建重复请求响应
            ReturnData data = new ReturnData();
            data.setCode(RestCodeConstants.CODE_REPEAT);
            data.setMsg(Const.CODE_REPEAT_MSG);
            return forbiddenIdempotent(exchange, data);
          }

          // 首次请求，设置过期时间并继续处理
          log.debug("首次请求通过 - 用户ID: {}, 方法: {}, URI: {}, 设置过期时间: {} ms",
              userId, method, requestUri, idempotentConfig.getExpireMill());

          return reactiveRedisTemplate.expire(key,
                  Duration.ofMillis(idempotentConfig.getExpireMill()))
              .then(chain.filter(exchange));  // 设置过期时间后继续过滤器链
        })
        .onErrorResume(throwable -> {
          // Redis操作异常时记录日志并放行请求，避免因Redis问题影响业务
          log.error("Redis操作异常，放行请求 - 用户ID: {}, 方法: {}, URI: {}",
              userId, method, requestUri, throwable);
          return chain.filter(exchange);
        });
  }

  /**
   * 获取过滤器执行顺序
   *
   * @return 过滤器顺序值
   */
  @Override
  public int getOrder() {
    return FilterOrder.IdempotentFilter;
  }

  /**
   * 构建Redis递增键名
   *
   * <p>键名格式: userId-method-url，确保每个用户的每个接口请求都有独立的计数器</p>
   *
   * @param userId 用户ID
   * @param method HTTP方法
   * @param url    请求URL
   * @return Redis键名
   */
  private String buildIncrementKey(String userId, String method, String url) {
    return userId + "-" + method + "-" + url;
  }

  /**
   * 返回防重提交拒绝响应
   *
   * <p>当检测到重复请求时，返回统一的错误响应</p>
   *
   * @param serverWebExchange 服务器Web交换对象
   * @param body              响应数据
   * @return 响应式结果
   */
  private Mono<Void> forbiddenIdempotent(ServerWebExchange serverWebExchange, ReturnData body) {
    // 设置响应状态码为200，但返回业务错误码
    serverWebExchange.getResponse().setStatusCode(HttpStatus.OK);

    // 将响应数据序列化为JSON字节数组
    byte[] bytes = JSONObject.toJSONString(body).getBytes(StandardCharsets.UTF_8);
    DataBuffer buffer = serverWebExchange.getResponse().bufferFactory().wrap(bytes);

    // 写入响应并完成
    return serverWebExchange.getResponse().writeWith(Flux.just(buffer));
  }
}
