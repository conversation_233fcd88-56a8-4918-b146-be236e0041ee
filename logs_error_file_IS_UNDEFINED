2025-06-05 13:44:54.669|ERROR| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:187|权限校验过程中发生异常
reactor.blockhound.BlockingOperationError: Blocking call! sun.misc.Unsafe#park
	at sun.misc.Unsafe.park(Unsafe.java)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.MonoFromPublisher] :
	reactor.core.publisher.Mono.from(Mono.java:513)
	io.lettuce.core.AbstractRedisReactiveCommands.createMono(AbstractRedisReactiveCommands.java:458)
Error has been observed at the following site(s):
	|_                       Mono.from ⇢ at io.lettuce.core.AbstractRedisReactiveCommands.createMono(AbstractRedisReactiveCommands.java:458)
	|_                        Mono.map ⇢ at org.springframework.data.redis.connection.lettuce.LettuceReactiveSetCommands.lambda$null$17(LettuceReactiveSetCommands.java:162)
	|_                  Flux.concatMap ⇢ at org.springframework.data.redis.connection.lettuce.LettuceReactiveSetCommands.lambda$sIsMember$18(LettuceReactiveSetCommands.java:157)
	|_                Mono.flatMapMany ⇢ at org.springframework.data.redis.connection.lettuce.LettuceReactiveRedisConnection.execute(LettuceReactiveRedisConnection.java:224)
	|_ MonoNext$NextSubscriber.onError ⇢ at io.lettuce.core.RedisPublisher$ImmediateSubscriber.onError(RedisPublisher.java:891)
	|_                 Flux.onErrorMap ⇢ at org.springframework.data.redis.connection.lettuce.LettuceReactiveRedisConnection.execute(LettuceReactiveRedisConnection.java:224)
	|_                       Flux.next ⇢ at org.springframework.data.redis.connection.ReactiveSetCommands.sIsMember(ReactiveSetCommands.java:545)
	|_                        Mono.map ⇢ at org.springframework.data.redis.connection.ReactiveSetCommands.sIsMember(ReactiveSetCommands.java:545)
	|_                  Flux.usingWhen ⇢ at org.springframework.data.redis.core.ReactiveRedisTemplate.doInConnection(ReactiveRedisTemplate.java:195)
	|_                       Mono.from ⇢ at org.springframework.data.redis.core.ReactiveRedisTemplate.createMono(ReactiveRedisTemplate.java:179)
	|_                Mono.subscribeOn ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.lambda$filter$4(AccessGatewayFilter.java:156)
	|_                    Mono.flatMap ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.lambda$filter$4(AccessGatewayFilter.java:157)
	|_                    Mono.flatMap ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.filter(AccessGatewayFilter.java:132)
Stack trace:
		at sun.misc.Unsafe.park(Unsafe.java)
		at java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
		at java.util.concurrent.locks.AbstractQueuedSynchronizer.parkAndCheckInterrupt(AbstractQueuedSynchronizer.java:836)
		at java.util.concurrent.locks.AbstractQueuedSynchronizer.acquireQueued(AbstractQueuedSynchronizer.java:870)
		at java.util.concurrent.locks.AbstractQueuedSynchronizer.acquire(AbstractQueuedSynchronizer.java:1199)
		at java.util.concurrent.locks.ReentrantLock$NonfairSync.lock(ReentrantLock.java:209)
		at java.util.concurrent.locks.ReentrantLock.lock(ReentrantLock.java:285)
		at java.util.concurrent.LinkedBlockingQueue.signalNotEmpty(LinkedBlockingQueue.java:172)
		at java.util.concurrent.LinkedBlockingQueue.offer(LinkedBlockingQueue.java:430)
		at java.util.AbstractQueue.add(AbstractQueue.java:95)
		at org.LatencyUtils.PauseDetector.addListener(PauseDetector.java:57)
		at org.LatencyUtils.TimeCappedMovingAverageIntervalEstimator$PauseTracker.<init>(TimeCappedMovingAverageIntervalEstimator.java:283)
		at org.LatencyUtils.TimeCappedMovingAverageIntervalEstimator.<init>(TimeCappedMovingAverageIntervalEstimator.java:71)
		at org.LatencyUtils.LatencyStats.<init>(LatencyStats.java:181)
		at org.LatencyUtils.LatencyStats$Builder.build(LatencyStats.java:390)
		at io.lettuce.core.metrics.DefaultCommandLatencyCollector$Latencies.<init>(DefaultCommandLatencyCollector.java:271)
		at io.lettuce.core.metrics.DefaultCommandLatencyCollector.lambda$recordCommandLatency$0(DefaultCommandLatencyCollector.java:114)
		at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
		at io.lettuce.core.metrics.DefaultCommandLatencyCollector.recordCommandLatency(DefaultCommandLatencyCollector.java:111)
		at io.lettuce.core.protocol.CommandHandler.recordLatency(CommandHandler.java:910)
		at io.lettuce.core.protocol.CommandHandler.decode(CommandHandler.java:766)
		at io.lettuce.core.protocol.CommandHandler.decode(CommandHandler.java:654)
		at io.lettuce.core.protocol.CommandHandler.channelRead(CommandHandler.java:594)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
2025-06-05 14:44:42.476|ERROR| N/A||main|o.s.b.d.LoggingFailureAnalysisReporter:40|

***************************
APPLICATION FAILED TO START
***************************

Description:

Field redisTemplate in cn.hydee.gateway.filter.YxtLoginAccessGatewayFilterFactory required a bean of type 'org.springframework.data.redis.core.RedisTemplate' that could not be found.

The injection point has the following annotations:
	- @org.springframework.beans.factory.annotation.Autowired(required=true)


Action:

Consider defining a bean of type 'org.springframework.data.redis.core.RedisTemplate' in your configuration.

