package cn.hydee.gateway.filter;

import static cn.hydee.gateway.filter.AccessGatewayFilter.HEAD_USER_ID_KEY;

import cn.hydee.gateway.config.IdempotentConfig;
import cn.hydee.gateway.constants.RestCodeConstants;
import cn.hydee.gateway.domain.ReturnData;
import cn.hydee.gateway.util.Const;
import com.alibaba.fastjson.JSONObject;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveValueOperations;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 防重提交过滤器 使用ReactiveRedisTemplate实现响应式Redis操作，避免阻塞
 *
 * <AUTHOR>
 * @version 2.0
 * @date 2020/2/18 13:50
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(IdempotentConfig.class)
public class IdempotentFilterClaude implements GlobalFilter, Ordered {

  @Autowired
  private IdempotentConfig idempotentConfig;

  @Autowired
  private ReactiveRedisTemplate<String, String> reactiveRedisTemplate;

  @Value("${idempotentReleaseOnOff:false}")
  private Boolean idempotentReleaseOnOff;

  @Override
  public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
    ServerHttpRequest request = exchange.getRequest();
    String requestUri = request.getPath().pathWithinApplication().value();
    String method = request.getMethodValue();

    // 检查当前请求是否需要进行幂等性校验
    if (!idempotentConfig.isContainWith(method, requestUri)) {
      log.debug("请求 {} {} 不在幂等性校验范围内，直接放行", method, requestUri);
      return chain.filter(exchange);
    }

    // 从请求头中获取用户ID
    String userId = request.getHeaders().getFirst(HEAD_USER_ID_KEY);
    if (userId == null || userId.trim().isEmpty()) {
      log.warn("请求头中未找到用户ID，跳过幂等性校验");
      return chain.filter(exchange);
    }

    // 构建Redis键
    String redisKey = buildIncrementKey(userId, method, requestUri);
    log.debug("开始幂等性校验，Redis键: {}", redisKey);

    return performIdempotentCheck(exchange, chain, redisKey);
  }

  /**
   * 执行幂等性检查
   *
   * @param exchange ServerWebExchange对象
   * @param chain    过滤器链
   * @param redisKey Redis键
   * @return Mono<Void>
   */
  private Mono<Void> performIdempotentCheck(ServerWebExchange exchange, GatewayFilterChain chain,
      String redisKey) {
    ReactiveValueOperations<String, String> valueOps = reactiveRedisTemplate.opsForValue();

    // 使用响应式方式进行Redis自增操作
    return valueOps.increment(redisKey)
        .flatMap(count -> {
          log.debug("Redis自增操作结果: {}", count);

          // 如果计数大于1，说明是重复请求
          if (count > 1) {
            log.warn("检测到重复请求，Redis键: {}, 计数: {}", redisKey, count);
            return handleDuplicateRequest(exchange);
          }

          // 首次请求，设置过期时间并继续处理
          return setKeyExpiration(redisKey)
              .then(chain.filter(exchange))
              .doOnSuccess(result -> log.debug("请求处理完成，Redis键: {}", redisKey))
              .doOnError(error -> {
                log.error("请求处理失败，清理Redis键: {}", redisKey, error);
                // 发生错误时清理Redis键，避免影响后续请求
                cleanUpRedisKey(redisKey);
              });
        })
        .onErrorResume(error -> {
          log.error("Redis操作异常，跳过幂等性校验: {}", error.getMessage(), error);
          // Redis异常时直接放行，避免影响正常业务
          return chain.filter(exchange);
        });
  }

  /**
   * 设置Redis键的过期时间
   *
   * @param redisKey Redis键
   * @return Mono<Boolean>
   */
  private Mono<Boolean> setKeyExpiration(String redisKey) {
    Duration expiration = Duration.ofMillis(idempotentConfig.getExpireMill());
    log.debug("设置Redis键过期时间: {} ms, 键: {}", idempotentConfig.getExpireMill(), redisKey);

    return reactiveRedisTemplate.expire(redisKey, expiration)
        .doOnSuccess(result -> {
          if (Boolean.TRUE.equals(result)) {
            log.debug("成功设置过期时间，Redis键: {}", redisKey);
          } else {
            log.warn("设置过期时间失败，Redis键: {}", redisKey);
          }
        })
        .onErrorResume(error -> {
          log.error("设置Redis键过期时间异常: {}", error.getMessage(), error);
          return Mono.just(false);
        });
  }

  /**
   * 处理重复请求
   *
   * @param exchange ServerWebExchange对象
   * @return Mono<Void>
   */
  private Mono<Void> handleDuplicateRequest(ServerWebExchange exchange) {
    ReturnData returnData = new ReturnData();
    returnData.setCode(RestCodeConstants.CODE_REPEAT);
    returnData.setMsg(Const.CODE_REPEAT_MSG);

    return buildErrorResponse(exchange, returnData);
  }

  /**
   * 清理Redis键（异步执行，不影响主流程）
   *
   * @param redisKey Redis键
   */
  private void cleanUpRedisKey(String redisKey) {
    reactiveRedisTemplate.delete(redisKey)
        .doOnSuccess(result -> log.debug("清理Redis键完成: {}", redisKey))
        .doOnError(error -> log.warn("清理Redis键失败: {}", redisKey, error))
        .subscribe(); // 异步执行，不阻塞主流程
  }

  @Override
  public int getOrder() {
    return FilterOrder.IdempotentFilter;
  }

  /**
   * 构建Redis自增键
   *
   * @param userId 用户ID
   * @param method HTTP方法
   * @param url    请求URL
   * @return Redis键
   */
  private String buildIncrementKey(String userId, String method, String url) {
    return String.join("-", userId, method, url);
  }

  /**
   * 构建错误响应
   *
   * @param exchange   ServerWebExchange对象
   * @param returnData 返回数据
   * @return Mono<Void>
   */
  private Mono<Void> buildErrorResponse(ServerWebExchange exchange, ReturnData returnData) {
    // 设置响应状态码
    exchange.getResponse().setStatusCode(HttpStatus.OK);

    // 序列化响应数据
    byte[] responseBytes = JSONObject.toJSONString(returnData).getBytes(StandardCharsets.UTF_8);
    DataBuffer dataBuffer = exchange.getResponse().bufferFactory().wrap(responseBytes);

    try {
      // 写入响应数据
      return exchange.getResponse().writeWith(Flux.just(dataBuffer));
    } finally {
      // 根据配置决定是否释放DataBuffer
      if (Boolean.TRUE.equals(idempotentReleaseOnOff)) {
        DataBufferUtils.release(dataBuffer);
      }
    }
  }
}