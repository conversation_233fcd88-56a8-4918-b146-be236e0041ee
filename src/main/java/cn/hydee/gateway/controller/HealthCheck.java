package cn.hydee.gateway.controller;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年01月19日 13:23
 * @email: <EMAIL>
 */
@RestController
@RequestMapping("/actuator")
public class HealthCheck {

    @Value("${defineHealthValue:{\"status\":\"UP\"}}")
    private String defineHealthValue;

    @GetMapping("/health")
    public JSONObject check() {
        return JSONObject.parseObject(defineHealthValue);
    }

}
