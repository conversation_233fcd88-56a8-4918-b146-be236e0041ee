route-forbidden-config:
  urlList:
    - /actuator/shutdown
server:
  port: 9000

  #logging:
  #level:
  #org.springframework.cloud.gateway: debug

alarm:
  sendErrorLog:
    enable: false

logging:
  level:
    root: INFO

spring:
  codec:
    max-in-memory-size: 512KB
  config:
    use-legacy-processing: true
  application:
    name: businesses-gateway
  profiles:
    active: local
  main:
    allow-bean-definition-overriding: true
  cloud:
    discovery:
      client:
        health-indicator:
          enabled: false
    nacos:
      discovery:
        server-addr: http://10.4.3.210:8848;
        namespace: 63b6732e-80fc-49c2-ac83-4b09e119d48c
        metadata:
          department: NR
        register-enabled: false
    sentinel:
      filter:
        enabled: false
      scg:
        fallback:
          enabled: true
          ## response返回文字提示信息，
          mode: response
          response-status: 200
          response-body: '{"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}'
          content-type: application/json
      transport:
        dashboard: test-sentinel.hxyxt.com
      eager: true
      data-source:
        ## 配置流控规则，名字任意
        flow:
          nacos:
            ## nacos的地址
            server-addr: 10.4.3.210:8848
            namespace: 9a3c9b59-885b-4a31-87c0-0bdc33328a5d
            ## 配置ID
            dataId: ${spring.application.name}-gateway-flow-rules.json
            ## 配置分组，默认是DEFAULT_GROUP
            groupId: SENTINEL_GROUP
            ## 配置存储的格式
            data-type: json
            ## rule-type设置对应得规则类型，总共七大类型，在com.alibaba.cloud.sentinel.datasource.RuleType这个枚举类中有体现
            rule-type: gw-flow
        ## 配置流控规则，名字任意
        api:
          nacos:
            ## nacos的地址
            server-addr: 10.4.3.210:8848
            namespace: 9a3c9b59-885b-4a31-87c0-0bdc33328a5d
            ## 配置ID
            dataId: ${spring.application.name}-gateway-api-rules.json
            ## 配置分组，默认是DEFAULT_GROUP
            groupId: SENTINEL_GROUP
            ## 配置存储的格式
            data-type: json
            ## rule-type设置对应得规则类型，总共七大类型，在com.alibaba.cloud.sentinel.datasource.RuleType这个枚举类中有体现
            rule-type: gw-api-group
        ## 配置降级规则，名字任意
        degrade:
          nacos:
            ## nacos的地址
            server-addr: 10.4.3.210:8848
            namespace: 13949598-55d3-46ac-b0e8-42a9c30a1601
            ## 配置ID
            dataId: ${spring.application.name}-degrade-rules.json
            ## 配置分组，默认是DEFAULT_GROUP
            groupId: SENTINEL_GROUP
            ## 配置存储的格式
            data-type: json
            ## rule-type设置对应得规则类型，总共七大类型，在com.alibaba.cloud.sentinel.datasource.RuleType这个枚举类中有体现
            rule-type: degrade
    inetutils:
      preferred-networks:
        - ^10\.200.+
    gateway:
      httpclient:
        connect-timeout: 2000
        response-timeout: 20s
        pool:
          max-idle-time: PT10S
          eviction-interval: PT30S
      globalcors:
        corsConfigurations:
          '[/**]':
            allowedOriginPatterns: "*"
            #            allowedOrigins: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true
      routes:
        # 通过ip
        - id: order-service-forBusinessGateway
          uri: http://**********:8080
          predicates:
            - Path=/dev-test/**
          filters:
            - StripPrefix=1
        # 从nacos获取
        - id: order-service-forBusinessGateway-b
          uri: lb://order-service-forBusinessGateway
          predicates:
            - Path=/dev-test-b/**
          filters:
            - StripPrefix=1

  redis:
    password: tThnBkJCgX
    jedis:
      pool:
        min-idle: 10
        max-active: 200
        max-idle: 50
        max-wait: 1000
    timeout: 1000
    cluster:
      nodes: **********:7000,**********:7001,**********:7000,**********:7001,**********:7000,**********:7001
      max-redirects: 3
api:
  base-info-version: 1.0

management:
  endpoint:
    mappings:
      enabled: true
    httptrace:
      enabled: true
    metrics:
      enabled: true
    health:
      enabled: true
      show-details: always
  health:
    defaults:
      enabled: false
  endpoints:
    web:
      exposure:
        include: ["*"]

feign:
  okhttp:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 30000
        readTimeout: 60000
        loggerLevel: full

rest:
  connectTimeout: 3000
  readTimeout: 6000
jwt:
  expire: 14400
  rsa-secret: xx1WET12^%3^(WE45
auth:
  user:
    token-header: Authorization

idempotent:
  expire-mill: 2000
  post-path:
    - /merchant/1.0/org
    - /merchant/1.0/authority
    - /merchant/1.0/employee
    - /operate/1.0/package
    - /operate/1.0/merchant
    - /operate/1.0/merchant/_purchasePkg
    - /operate/1.0/authority
    - /operate/1.0/authority/_createAcc
    - /mer-manager/1.0/pageset
    - /mer-manager/1.0/commodity
    - /mer-manager/1.0/commodity/self
    - /mer-manager/1.0/comm-dimen/add
    - /mer-manager/1.0/comm-type/addType
    - /mer-manager/1.0/assemble-comm
    - /promote/1.0/admin/activities
    - /mer-manager/1.0/csd-msg
    - /mask/1.0/b/store/_save
    - /mask/1.0/b/product/_save
    - /hydee-ewx-honey/1.0/clientStoreInspectTaskController/firstSubmit
    - /c/common/config/r/1.0/listGatewayRouteRule
    - /dev-test/forBusinessGateway/1
gate:
  ignore:
    start-with:
      - /datasync/
      - /promote/
      - /open
      - /customer/
      - /ydjia-statistic/
      - /payment/1.0/receive/notify
      - /payment/1.0/receive/return
      - /payment/payment/payNotify
      - /payment/payment/refundNotify
      - /payment/payment/sharingNotify
      - /payment/1.0/unifiedPay/payNotify
      - /payment/1.0/unifiedPay/refundNotify
      - /payment/account/settleNotify
      - /merchant/1.0/acc/_queryAccountStatus/
      - /merchant/1.0/verification/
      - /merchant/1.0/acc/_activateAccount
      - /merchant/1.0/acc/getSelfBuildAppLoginUrl
      - /merchant/1.0/acc/check_user
      - /merchant/1.0/acc/checkShopStaff
      - /merchant/1.0/acc/update/avatar
      - /merchant/1.0/file/_upload
      - /mer-manager/1.0/express-record
      - /mer-manager/1.0/weeChatOpen/auth
      - /mer-manager/1.0/weeChatOpen/recvMsg/
      - /middle-third/1.0/open/receiveTicket
      - /middle-third/1.0/open/recvMsg/
      - /middle-third/1.0/open/authCallBack
      - /honey-medical/v1/mobileUser/getToken
      - /honey-medical/v1/monitor/healthCheck
      - /honey-medical/v1/scanLogin/getScanElement
      - /honey-medical/v1/scanLogin/isAppLoginCompleted
      - /honey-medical/v1/sso/getSSOToken
      - /merchandise/1.0/store-spec/sync
      - /merchandise/1.0/ds/op/file
      - /merchandise/1.0/sync/stock
      - /merchandise/1.0/sync/price
      - /merchandise/1.0/sync/commAndStore
      - /merchandise/1.0/open
      - /merchandise/1.0/comm-spec/access-plat
      - /mask/1.0/c
      - /dscloud/1.0/ds/print
      - /dscloud/1.0/ds/file
      - /dscloud/1.0/ds/order/callback/orderDelivery
      - /hydee-ewx-service/1.0/honeyApp/dataCallback
      - /hydee-ewx-service/1.0/honeyApp/orderCallback
      - /hydee-ewx-service/1.0/innerHoneyApp/contactCallback
      - /hydee-ewx-service/1.0/innerHoneyApp/customerCallback
      - /hydee-ewx-service/1.0/customHoneyApp/callback
      - /hydee-ewx-honey/1.0/third/plat/callBack
      - /hydee-ewx-honey/1.0/third/plat/authCallBack
      - /hydee-ewx-honey/1.0/third/plat/recvMsg
      - /hydee-ewx-honey/1.0/ewxService/getMiniInfo
      - /hydee-ewx-honey/1.0/wxclient
      - /hydee-ewx-honey/1.0/boss
      - /hydee-ewx-honey/1.0/login/fromAdmin
      - /hydee-ewx-honey/1.0/login/fromMobile
      - /hydee-ewx-honey/1.0/login/fromInner
      - /hydee-ewx-honey-py/1.0/login/fromAdmin
      - /hydee-ewx-honey-py/1.0/login/fromMobile
      - /hydee-live-service/1.0/videoCallback/recordVideoCallback
      - /hydee-live-service/1.0/videoCallback/pushVideoCallback
      - /member/1.0/memberManage/changeMemberCard
      - /member/1.0/erpMember/incrSyncMember
      - /member/1.0/memberCrowd/getExpertsCrowdInfo
      - /member/1.0/memberCrowd/experts/delete
      - /market/1.0/couponGift/order/callback
      - /mer-manager/1.0/oss/file/upload
      - /mer-manager/1.0/merchantSwitch/batchUpdate
      - /syncerp/1.0.0/pushCompany
      - /syncerp/1.0.0/pushStore
      - /syncerp/1.0.0/pushEmployee
      - /merchant/1.0/cjksso/getToken
      - /merchant/1.0/cjksso/_login
      - /sp-platform/1.0/spAcc/_login
      - /sp-platform/1.0/spAcc/_register
      - /sp-platform/1.0/spAcc/notLoginCheck
      - /sp-platform/1.0/spAcc/_sendCode
      - /sp-platform/1.0/spAcc/_checkVerificationCode
      - /sp-platform/1.0/file/_upload
      - /sp-platform/1.0/spAcc/forgetPassword
      - /ydjia-report/1.0/admin
      - /ydjia-report/vs
      - /ydjia-adaptation
      - /drug/
      - /hydee-middle-alerting/1.0/admin
      - /hydee-middle-alerting/vs
      - /b2c/1.0/oms/order/listByTime
      - /b2c/1.0/oms/order/listRefundByTime
      - /account-center/him/merchant/getList
      - /account-center/him/provider/getList
      - /market/1.0/ispActivity4Him/getDataByMerchant
      - /market/1.0/ispActivity4Him/getDataByIsp
      - /merchant/1.0/acc/_forgetPassword
      - /sp-platform/1.0/spAcc/search/
      - /merchant/1.0/acc/account/query/
      - /assist-synthesis/swagger
      - /assist-synthesis/v2/api-docs
      - /assist-synthesis/webjars
      - /assist-synthesis/swagger-resources
      - /assist-synthesis/doc.html
      - /assist-synthesis/druid
      - /assist-core-toolkit/swagger
      - /assist-core-toolkit/v2/api-docs
      - /assist-core-toolkit/webjars
      - /assist-core-toolkit/swagger-resources
      - /assist-core-toolkit/doc.html
      - /assist-core-toolkit/druid
      - /assist-growth/swagger
      - /assist-growth/v2/api-docs
      - /assist-growth/webjars
      - /assist-growth/swagger-resources
      - /assist-growth/druid
      - /assist-home/swagger
      - /assist-home/druid
      - /assist-home/v2/api-docs
      - /assist-home/webjars
      - /assist-home/swagger-resources
      - /assist-home/doc.html
      - /assist-task/swagger
      - /assist-task/v2/api-docs
      - /assist-task/webjars
      - /assist-task/swagger-resources
      - /assist-task/doc.html
      - /assist-task/druid
      - /assist-middle-portal/swagger
      - /assist-middle-portal/v2/api-docs
      - /assist-middle-portal/webjars
      - /assist-middle-portal/swagger-resources
      - /assist-middle-portal/doc.html
      - /assist-middle-portal/druid
      - /assist-middle-portal/c/common/weChat/r/1.0/getJsSdkAuthConfig
      - /assist-middle-portal/c/material/w/1.0/doSaveMaterialVisit
      - /yxt-basis/swagger
      - /yxt-basis/v2/api-docs
      - /yxt-basis/webjars
      - /yxt-basis/swagger-resources
      - /yxt-basis/doc.html
      - /yxt-basis/druid
      - /yxt-export/swagger
      - /yxt-export/v2/api-docs
      - /yxt-export/webjars
      - /yxt-export/druid
      - /yxt-export/swagger-resources
      - /yxt-export/doc.html
      - /yxt-app-push/swagger
      - /yxt-app-push/v2/api-docs
      - /yxt-app-push/webjars
      - /yxt-app-push/swagger-resources
      - /yxt-app-push/druid
      - /assist-synthesis/b/common/disposition/r/1.0/listPolling
      - /assist-synthesis/c/common/disposition/r/1.0/listPolling
      - /assist-synthesis/c/appVersion/r/1.0/queryLatestVersion
      - /assist-synthesis/b/appVersion/w/1.0/createAppVersionBySign
      - /decision/swagger
      - /decision/v2/api-docs
      - /decision/webjars
      - /decision/swagger-resources
      - /decision/doc.html
      - /assist-prospect/common/auth/r/1.0/checkAuthBeforeLogin
      - /assist-home/c/common/front/r/1.0/checkAuthBeforeLogin
      - /assist-home/c/common/front/r/2.0/checkAuthBeforeLogin
      - /yxt-medical-prescription/api/1.0/medical/staff/checkUploadSign
      - /yxt-medical-prescription/api/1.0/medical/staff/uploadSign
      - /assist-synthesis/b/appVersion/w/1.0/addAppVersionInfoAndResource
      - /assist-synthesis/b/appVersion/r/1.0/getLatestAppVersionResource
      - /assist-synthesis/b/appVersion/r/1.0/getLatestInstallPackage
      - /assist-synthesis/c/appVersion/r/1.0/getUpdateResource
      - /assist-synthesis/b/appVersion/r/1.0/getAppVersion
      - /assist-synthesis/b/appVersion/r/2.0/getLatestAppVersionResource
      - /bigdata-alarm/data/check/r/1.0/es/cargo/push
      - /bigdata-alarm/data/check/r/1.0/data/error/phone
      - /bigdata-alarm/data/check/r/1.0/data/receive/phone
      - /assist-hcm/swagger
      - /assist-hcm/v2/api-docs
      - /assist-hcm/webjars
      - /assist-hcm/swagger-resources
      - /assist-hcm/doc.html
      - /assist-hcm/druid
      - /mind/swagger
      - /mind/v2/api-docs
      - /mind/webjars
      - /mind/swagger-resources
      - /mind/doc.html
      - /mind/commission/msg/r/1.0/qw/receive
      - /yxt-login/swagger
      - /yxt-login/v2/api-docs
      - /yxt-login/webjars
      - /yxt-login/swagger-resources
      - /yxt-login/doc.html
      - /yxt-login/druid
      - /yxt-mall-b2b/swagger
      - /yxt-mall-b2b/v2/api-docs
      - /yxt-mall-b2b/webjars
      - /yxt-mall-b2b/swagger-resources
      - /yxt-mall-b2b/doc.html
      - /yxt-mall-b2b/druid
      - /yxt-basis-prospect/swagger
      - /yxt-basis-prospect/v2/api-docs
      - /yxt-basis-prospect/webjars
      - /yxt-basis-prospect/swagger-resources
      - /yxt-basis-prospect/doc.html
      - /yxt-basis-prospect/druid
      # 网关放过路由
      - /yxt-login/c/cas/w/1.0/authentication
      - /yxt-login/outer/cas/r/1.0/authentication
      #   - /yxt-login/b/account/w/1.0/modifyPassword
      - /assist-middle-portal/c/userdevice/w/1.0/submitAppHeartbeat
      - /assist-middle-portal/c/common/config/r/1.0/listGatewayRouteRule
      - /yxt-medical-prescription/third/1.0/lianOu/inquiryCallback
    end-with:
      - /_login
      - /login
      - /loginByPhone
      - /api-docs
      - /doc.html
      - /druid
      - /swagger-resources
      - /webjars
      - /swagger
    log-end:
      - /_login



token:
  resolver:
    #        igrone:
    #            mercodes: 999999,hydee,SPHYDEE
    body:
      enable: true

flowRules:
  '[
    {
        "resource":"ydjia-merchant-platform",
        "resourceMode":0,
        "count":10000,
        "intervalSec":1,
        "paramItem":{
            "parseStrategy":2,
            "fieldName":"merCode"
        }
    },
    {
        "resource":"hydee-business-order-web",
        "resourceMode":0,
        "count":10000,
        "intervalSec":1,
        "paramItem":{
            "parseStrategy":2,
            "fieldName":"merCode"
        }
    },
    
    {
        "resource":"data_sync_third_callback",
        "resourceMode":1,
        "count":2,
        "intervalSec":10
    }
  ]'
apiDefinitions:
  '[
    {
        "apiName":"data_sync_third_callback",
        "predicateItems":[
            {
                "pattern":"/data-sync/third/callback/27/selectStock/**",
                "matchStrategy":1
            }
        ]
    }
  ]'

grey:
  enable: true
  local-mappings:
    '[(.+)]': $1.svc.k8s.test.hxyxt.com

# 动态路由功能开关
dynamic:
  enable: true

safe-center:
  gateway-channel: B
  auth:
    enable:
      list: yxt-safe-cente