<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <springProperty scope="context" name="serviceName" source="spring.application.name"/>

    <springProperty scope="context" name="logs_info_file" source="logs.info.file"/>
    <springProperty scope="context" name="logs_error_file" source="logs.error.file"/>
    <springProperty scope="context" name="logs_audit_file" source="logs.audit.file"/>

    <!-- 日志格式: 时间|日志级别|链路ID|用户id|线程名称|日志类:日志行|日志内容  -->
    <property name="pattern"
      value="%d{yyyy-MM-dd HH:mm:ss.SSS}|%level|%replace(%X{tid}){'TID:', ''}|%X{userId}|%thread|%logger{36}:%line|%msg%n"/>

    <appender name="LOCAL_CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="com.yxt.common.alarm.MyTraceIdMDCPatternLogbackLayout">
            <pattern>
                ${pattern}
            </pattern>
        </layout>
    </appender>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender" addtivity="false">
        <encoder charset="UTF-8"
          class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <fieldName>@timestamp</fieldName>
                    <pattern>yyyy-MM-dd HH:mm:ss.SSS</pattern>
                    <timeZone>GMT+8</timeZone>
                </timestamp>
                <pattern>
                    <pattern>
                        {
                        "level":"%level",
                        "traceId":"%replace(%X{tid}){'TID:', ''}",
                        "userId":"%X{userId}",
                        "app":"%property{serviceName}",
                        "thread":"%thread",
                        "method":"%logger{36}:%line",
                        "msg":"%msg",
                        "stackTrace": "%exception"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logs_info_file}</file>
        <layout class="com.yxt.common.alarm.MyTraceIdMDCPatternLogbackLayout">
            <Pattern>${pattern}</Pattern>
        </layout>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logs_info_file}.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <MaxHistory>7</MaxHistory>
            <maxFileSize>300MB</maxFileSize>
        </rollingPolicy>
    </appender>

    <appender name="ASYNC_INFO_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <includeCallerData>true</includeCallerData>
        <!-- 如果队列的90%已满,则会丢弃TRACT、DEBUG、INFO级别的日志  ch.qos.logback.classic.AsyncAppender.isDiscardable -->
        <discardingThreshold>10</discardingThreshold>
        <queueSize>512</queueSize>
        <!-- 队列满了不阻塞调用者  ch.qos.logback.core.AsyncAppenderBase.put -->
        <neverBlock>true</neverBlock>
        <appender-ref ref="FILE"/>
    </appender>

    <appender name="ERR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <file>${logs_error_file}</file>
        <layout class="com.yxt.common.alarm.MyTraceIdMDCPatternLogbackLayout">
            <Pattern>${pattern}</Pattern>
        </layout>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logs_error_file}.%d{yyyy-MM-dd}</fileNamePattern>
            <MaxHistory>7</MaxHistory>
        </rollingPolicy>
    </appender>

    <appender name="ASYNC_ERROR_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <includeCallerData>true</includeCallerData>
        <!-- 队列满了不阻塞调用者  ch.qos.logback.core.AsyncAppenderBase.put -->
        <neverBlock>true</neverBlock>
        <appender-ref ref="ERR_FILE"/>
    </appender>

    <appender name="AUDIT_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <file>${logs_audit_file}</file>
        <layout class="com.yxt.common.alarm.MyTraceIdMDCPatternLogbackLayout">
            <Pattern>${pattern}</Pattern>
        </layout>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logs_audit_file}.%d{yyyy-MM-dd}</fileNamePattern>
            <MaxHistory>7</MaxHistory>
        </rollingPolicy>
    </appender>

    <appender name="SEND_LOG" class="com.yxt.common.alarm.util.AppAlarmAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="SEND_WARN_LOG" class="com.yxt.common.alarm.util.AppAlarmWarnLogAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <springProfile name="pro,pre,test,dev">
        <root level="INFO">
            <appender-ref ref="ASYNC_INFO_FILE"/>
            <appender-ref ref="ASYNC_ERROR_FILE"/>
            <appender-ref ref="SEND_LOG"/>
            <appender-ref ref="SEND_WARN_LOG"/>
            <appender-ref ref="CONSOLE"/>
        </root>
        <logger name="AuditLog" additivity="true">
            <appender-ref ref="AUDIT_FILE"/>
        </logger>
    </springProfile>

    <springProfile name="!pro,!pre,!test,!dev">
        <root level="INFO">
            <appender-ref ref="LOCAL_CONSOLE"/>
            <appender-ref ref="ASYNC_INFO_FILE"/>
            <appender-ref ref="ASYNC_ERROR_FILE"/>
        </root>
    </springProfile>

</configuration>